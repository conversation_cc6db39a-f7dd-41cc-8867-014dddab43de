"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import type { ChatStatus } from "@/types/chat"
import { Maximize2Icon, MaximizeIcon, MinimizeIcon, XIcon } from "lucide-react"

export interface ChatHeaderProps {
	title: string
	status: ChatStatus
	onMinimize: () => void
	isMinimized: boolean
	onMaximize?: () => void
	isMaximized?: boolean
	onClose?: () => void
}

export function ChatHeader({
	title,
	status,
	onMinimize,
	isMinimized,
	onClose,
}: ChatHeaderProps) {
	return (
		<div className="flex items-center justify-between p-3 border-b border-zinc-800 bg-zinc-900 widget-header">
			<div className="flex items-center gap-2">
				<div
					className={cn(
						"w-2 h-2 rounded-full",
						status === "idle" && "bg-green-500",
						status === "thinking" && "bg-amber-500 animate-pulse",
						status === "error" && "bg-red-500",
					)}
				/>
				<h2 className="text-sm font-medium text-zinc-300">{title}</h2>
			</div>

			<div className="flex items-center gap-1">

				<Button
					variant="ghost"
					size="icon"
					onClick={onMinimize}
					className="h-8 w-8 text-zinc-500 hover:text-zinc-300 hover:bg-zinc-800"
					aria-label={isMinimized ? "Maximize chat" : "Minimize chat"}
				>
					{isMinimized ? (
						<MaximizeIcon className="w-4 h-4" />
					) : (
						<MinimizeIcon className="w-4 h-4" />
					)}
				</Button>
			</div>
		</div>
	)
}
