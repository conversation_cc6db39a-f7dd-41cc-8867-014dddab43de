"use client"

import { Card } from "@/components/ui/card"
import { <PERSON>roll<PERSON><PERSON> } from "@/components/ui/scroll-area"
import { cn, generateStableId } from "@/lib/utils"
import type { ChatRole } from "@/types/chat"
import { useChat } from "@ai-sdk/react"
import { useCallback, useEffect, useRef, useState } from "react"
import React from "react"
import { toast } from "sonner"
import { v4 as uuidv4 } from "uuid"
import { ChatHeader } from "./ChatHeader"
import { ChatInput } from "./ChatInput"
import { ChatMessages } from "./ChatMessages"
import { ChatToggleButton } from "./ChatToggleButton"

export interface ChatUIProps {
	title?: string
	position?: "bottom-right" | "bottom-left"
	placeholder?: string
	initialMessages?: Array<{ role: "user" | "assistant"; content: string }>
	className?: string
	style?: React.CSSProperties
	websiteId?: string
	visitorId?: string
	primaryColor?: string
	secondaryColor?: string
	initiallyOpen?: boolean
	isWidget?: boolean // Flag to identify if this is an embedded widget
}

// Move ErrorBoundary outside ChatUI
class ErrorBoundary extends React.Component<
	{ children: React.ReactNode },
	{ hasError: boolean }
> {
	constructor(props: { children: React.ReactNode }) {
		super(props)
		this.state = { hasError: false }
	}
	static getDerivedStateFromError(error: Error) {
		return { hasError: true }
	}
	componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
		// You may want to call a global error tracker here
	}
	render() {
		if (this.state.hasError) {
			return (
				<div className="p-4 text-red-600 bg-red-50 border border-red-200 rounded">
					An unexpected error occurred in the chat widget.
				</div>
			)
		}
		return this.props.children
	}
}

export function ChatUI({
	title = "Chat Assistant",
	position = "bottom-right",
	placeholder = "Type your message...",
	initialMessages = [],
	className,
	style,
	websiteId,
	visitorId,
	primaryColor = "#4F46E5",
	secondaryColor = "#FFFFFF",
	initiallyOpen = false,
	isWidget = false,
}: ChatUIProps) {
	const [isOpen, setIsOpen] = useState(initiallyOpen)
	const [isMaximized, setIsMaximized] = useState(false)
	const [isClient, setIsClient] = useState(false)
	const messagesEndRef = useRef<HTMLDivElement>(null)

	useEffect(() => {
		setIsClient(true)

		// Add fullscreen class on mobile devices or when maximized
		const handleResize = () => {
			const isMobile = window.innerWidth < 640
			const chatElement = document.querySelector(".chat-widget")
			if (chatElement) {
				if ((isMobile && isOpen) || isMaximized) {
					chatElement.classList.add("chat-fullscreen")
				} else {
					chatElement.classList.remove("chat-fullscreen")
				}
			}
		}

		window.addEventListener("resize", handleResize)
		handleResize() // Initial check

		return () => window.removeEventListener("resize", handleResize)
	}, [isOpen, isMaximized])

	// Generate a stable visitor ID if not provided
	const [generatedVisitorId] = useState(
		() => visitorId || generateStableId("visitor"),
	)

	// Generate a conversation ID
	const [conversationId] = useState(() => uuidv4())

	// Track conversation start time for analytics
	const conversationStartTime = useRef(Date.now())

	// State for file attachments
	const [attachedFiles, setAttachedFiles] = useState<File[]>([])

	// Generate stable IDs for initial messages
	const [processedInitialMessages] = useState(() =>
		initialMessages.map((msg) => ({
			...msg,
			id: generateStableId("msg"),
		})),
	)

	// Track chat events
	const trackEvent = useCallback(
		async (eventType: string, metadata?: Record<string, unknown>) => {
			try {
				if (!websiteId) return

				// Include isWidget flag in all events
				const enhancedMetadata = {
					...metadata,
					isWidget,
					source: isWidget ? "embedded" : "direct",
				}

				await fetch("/api/analytics/track-event", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						websiteId,
						visitorId: generatedVisitorId,
						conversationId,
						eventType,
						metadata: enhancedMetadata,
					}),
				})
			} catch (error) {
				console.error("Error tracking event:", error)
			}
		},
		[websiteId, generatedVisitorId, conversationId, isWidget],
	)

	// Track performance metrics
	const trackPerformance = useCallback(
		async (responseTime: number, ragUsed: boolean, ragResultCount: number) => {
			try {
				if (!websiteId) return

				await fetch("/api/analytics/track-performance", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						websiteId,
						conversationId,
						responseTime,
						ragUsed,
						ragResultCount,
						isWidget,
						source: isWidget ? "embedded" : "direct",
					}),
				})
			} catch (error) {
				console.error("Error tracking performance:", error)
			}
		},
		[websiteId, conversationId, isWidget],
	)

	// Track conversation started event when the component mounts
	useEffect(() => {
		if (websiteId) {
			trackEvent("conversation_started", {
				initiallyOpen,
				// isWidget is already included by the trackEvent function
			})
		}
	}, [websiteId, initiallyOpen, trackEvent])

	const { messages, input, handleInputChange, handleSubmit, error, status } =
		useChat({
			api: websiteId ? `/api/chat/${websiteId}` : "/api/chat",
			body: {
				websiteId,
				visitorId: generatedVisitorId,
				conversationId,
			},
			initialMessages: processedInitialMessages,
			onError: (err) => {
				toast.error("An error occurred", {
					description: err.message || "Failed to send message",
				})
				console.error("Chat error:", err)

				// Track error event
				trackEvent("error", {
					message: err.message || "Unknown error",
				})
			},
		})

	// Auto-scroll to bottom when messages change or during AI typing
	useEffect(() => {
		if (isClient && isOpen && messagesEndRef.current) {
			// Ensure we scroll to the bottom of the messages
			messagesEndRef.current.scrollIntoView({
				behavior: "smooth",
				block: "end",
			})
		}
	}, [isClient, isOpen])

	// Auto-scroll when AI starts typing (status changes to "submitted")
	useEffect(() => {
		if (
			isClient &&
			isOpen &&
			status === "submitted" &&
			messagesEndRef.current
		) {
			messagesEndRef.current.scrollIntoView({
				behavior: "smooth",
				block: "end",
			})
		}
	}, [isClient, isOpen, status])

	// Track message events and performance metrics
	useEffect(() => {
		if (!isClient || !websiteId || messages.length === 0) return

		// Get the last message
		const lastMessage = messages[messages.length - 1]

		// Skip initial messages
		if (lastMessage.id?.startsWith("msg-")) return

		// Track message events
		if (lastMessage.role === "user") {
			trackEvent("message_sent", {
				content: lastMessage.content.substring(0, 100), // Only store first 100 chars for privacy
			})
		} else if (lastMessage.role === "assistant") {
			// Calculate response time
			const responseTime = Date.now() - conversationStartTime.current

			// Track message received event
			trackEvent("message_received", {
				contentLength: lastMessage.content.length,
			})

			// Track performance metrics
			trackPerformance(responseTime, true, 0) // Assuming RAG is used

			// Reset the start time for the next message
			conversationStartTime.current = Date.now()

			// Auto-scroll to bottom when assistant message content changes (streaming)
			if (messagesEndRef.current) {
				messagesEndRef.current.scrollIntoView({
					behavior: "smooth",
					block: "end",
				})
			}
		}
	}, [isClient, websiteId, messages, trackEvent, trackPerformance])

	// Listen for postMessage events from the parent window
	useEffect(() => {
		if (!isClient) return

		const handleMessage = (event: MessageEvent) => {
			if (event.data?.action) {
				console.log("ChatUI: Received message from parent:", event.data.action)

				switch (event.data.action) {
					case "open":
						console.log("ChatUI: Setting isOpen to true")
						setIsOpen(true)

						// Force a re-render to ensure the chat is displayed
						setTimeout(() => {
							if (messagesEndRef.current) {
								messagesEndRef.current.scrollIntoView({
									behavior: "smooth",
									block: "end",
								})
							}
						}, 100)
						break
					case "close":
						console.log("ChatUI: Setting isOpen to false")
						setIsOpen(false)
						break
					case "toggle":
						console.log("ChatUI: Toggling isOpen state")
						setIsOpen((prev) => !prev)
						break
					case "maximize":
						setIsMaximized(true)
						break
					case "restore":
						setIsMaximized(false)
						break
					case "toggleMaximize":
						setIsMaximized((prev) => !prev)
						break
				}
			}
		}

		window.addEventListener("message", handleMessage)
		return () => window.removeEventListener("message", handleMessage)
	}, [isClient])

	const handleToggle = () => {
		console.log("ChatUI: handleToggle called, current isOpen state:", isOpen)

		const newState = !isOpen
		console.log("ChatUI: Setting isOpen to:", newState)

		// Set the state
		setIsOpen(newState)

		// Track toggle event
		if (websiteId) {
			trackEvent(newState ? "chat_opened" : "chat_minimized")
		}

		// If closing and in an iframe, send message to parent
		if (!newState && isWidget && window.parent && window !== window.parent) {
			console.log("ChatUI: Sending close message to parent")
			window.parent.postMessage({ action: "close" }, "*")
		}

		// If we're opening the chat, ensure the messages are scrolled into view
		if (newState && messagesEndRef.current) {
			setTimeout(() => {
				if (messagesEndRef.current) {
					console.log("ChatUI: Scrolling messages into view")
					messagesEndRef.current.scrollIntoView({
						behavior: "smooth",
						block: "end",
					})
				}
			}, 100)
		}
	}

	const handleMaximize = () => {
		const newState = !isMaximized
		setIsMaximized(newState)

		// Track maximize event
		if (websiteId) {
			trackEvent(newState ? "chat_maximized" : "chat_restored")
		}
	}

	const handleClose = () => {
		// Close the chat
		setIsOpen(false)

		// Track close event
		if (websiteId) {
			trackEvent("chat_closed")
		}

		// If in an iframe, send message to parent
		if (isWidget && window.parent && window !== window.parent) {
			window.parent.postMessage({ action: "close" }, "*")
		}
	}

	// Show the toggle button when chat is closed (but not when embedded as a widget)
	// When isWidget is true, the external bubble button handles the toggle functionality
	if (isClient && !isOpen) {
		if (isWidget) {
			// When embedded as a widget and closed, render nothing
			// The external bubble button handles the toggle functionality
			console.log("ChatUI: Widget is closed, rendering nothing (external bubble handles toggle)")
			return null
		} else {
			// When not embedded, show the internal toggle button
			return (
				<ChatToggleButton
					isOpen={isOpen}
					onClick={handleToggle}
					primaryColor={primaryColor}
					position={position}
				/>
			)
		}
	}

	// Use ErrorBoundary in the render
	if (error) {
		return (
			<div className="p-4 text-red-600 bg-red-50 border border-red-200 rounded">
				An unexpected error occurred in the chat widget.
			</div>
		)
	}

	return (
		<>
			{isClient && (
				<ErrorBoundary>
					<Card
						style={
							{
								...style,
								"--chat-primary-color": primaryColor,
								"--chat-secondary-color": secondaryColor,
							} as React.CSSProperties
						}
						className={cn(
							"chat-widget fixed z-50 flex flex-col",
							"w-[360px] h-[600px] max-h-[calc(100vh-2rem)]",
							"shadow-lg rounded-lg overflow-hidden",
							"transition-all duration-300 ease-in-out",
							"bg-zinc-950 text-zinc-300 border border-zinc-800",
							isMaximized ? "h-[calc(100vh-2rem)] w-[calc(100vw-2rem)]" : "",
							position === "bottom-right"
								? "bottom-4 right-4"
								: "bottom-4 left-4",
							className,
						)}
					>
						<ChatHeader
							title={title}
							status={
								status === "submitted" ? "thinking" : error ? "error" : "idle"
							}
							onMinimize={handleToggle}
							isMinimized={false}
							onMaximize={handleMaximize}
							isMaximized={isMaximized}
							onClose={handleClose}
						/>

						<div className="flex-1 flex flex-col overflow-hidden bg-zinc-950">
							<ScrollArea className="flex-1">
								<div className="p-4">
									<ChatMessages
										messages={messages.map((m) => {
											// Use a stable timestamp to prevent hydration errors
											const timestamp = isClient ? new Date() : new Date(0)

											return {
												id: m.id || generateStableId("msg"),
												content: m.content,
												role: m.role as ChatRole,
												timestamp,
											}
										})}
										status={
											status === "submitted"
												? "thinking"
												: error
													? "error"
													: "idle"
										}
									/>
									{/* Empty div at the end for scrolling to bottom */}
									<div ref={messagesEndRef} className="h-1 mt-1" />
								</div>
							</ScrollArea>

							<ChatInput
								value={input}
								onChange={handleInputChange}
								onSubmit={(e) => {
									e.preventDefault()
									handleSubmit(e)
								}}
								disabled={status === "submitted"}
								placeholder={placeholder}
							/>
						</div>
						<p className="text-xs text-center chat-copy">
							Powered by{" "}
							<a
								href="https://bublai.com"
								target="_blank"
								rel="noopener noreferrer"
							>
								Bubl
							</a>
						</p>
					</Card>
				</ErrorBoundary>
			)}
		</>
	)
}
