/**
 * Bubl Widget V2 - Complete Rebuild
 *
 * Features:
 * - Shadow DOM isolation
 * - Zero style leakage
 * - Self-contained bundle
 * - Modern Web Components
 * - Performance optimized
 *
 * @version 2.0.0
 */

(function() {
  'use strict';

  // Prevent multiple widget instances
  if (window.BublWidgetV2) {
    console.warn('Bubl Widget V2 already loaded');
    return;
  }

  // Widget configuration interface
  interface WidgetConfig {
    websiteId: string;
    primaryColor?: string;
    secondaryColor?: string;
    position?: 'bottom-right' | 'bottom-left';
    welcomeMessage?: string;
    headerText?: string;
    initiallyOpen?: boolean;
    apiBaseUrl?: string;
  }

  // CSS-in-JS styles for complete isolation
  const WIDGET_STYLES = `
    :host {
      /* Reset all inherited styles */
      all: initial;

      /* Set font family */
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

      /* Positioning */
      position: fixed !important;
      z-index: 2147483647 !important;

      /* Ensure no interference with host */
      pointer-events: none !important;
      user-select: none !important;

      /* Prevent layout shifts */
      contain: layout style paint !important;
    }

    .widget-container {
      /* Reset and isolate */
      all: unset;
      display: block;

      /* Enable pointer events for widget */
      pointer-events: auto;

      /* Positioning */
      position: relative;

      /* Prevent text selection */
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
    }

    .bubble-button {
      /* Reset */
      all: unset;

      /* Layout */
      display: flex;
      align-items: center;
      justify-content: center;

      /* Size */
      width: 60px;
      height: 60px;

      /* Appearance */
      border-radius: 50%;
      background: var(--primary-color, #4F46E5);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      /* Interaction */
      cursor: pointer;
      pointer-events: auto;

      /* Animation */
      transition: all 0.2s ease;
      transform: translateZ(0);
      will-change: transform;
    }

    .bubble-button:hover {
      transform: scale(1.05) translateZ(0);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }

    .bubble-button:active {
      transform: scale(0.95) translateZ(0);
    }

    .bubble-icon {
      width: 24px;
      height: 24px;
      fill: white;
    }

    .chat-container {
      /* Reset */
      all: unset;

      /* Layout */
      display: none;
      flex-direction: column;

      /* Size */
      width: 360px;
      height: 600px;
      max-height: calc(100vh - 40px);

      /* Appearance */
      background: #1a1a1a;
      border-radius: 12px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      overflow: hidden;

      /* Animation */
      transition: all 0.3s ease;
      transform: translateZ(0);
    }

    .chat-container.open {
      display: flex;
    }

    .chat-header {
      /* Layout */
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;

      /* Appearance */
      background: #2a2a2a;
      border-bottom: 1px solid #3a3a3a;

      /* Text */
      color: white;
      font-size: 14px;
      font-weight: 500;
    }

    .close-button {
      /* Reset */
      all: unset;

      /* Layout */
      display: flex;
      align-items: center;
      justify-content: center;

      /* Size */
      width: 24px;
      height: 24px;

      /* Appearance */
      border-radius: 4px;

      /* Interaction */
      cursor: pointer;

      /* Animation */
      transition: background-color 0.2s ease;
    }

    .close-button:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    .close-icon {
      width: 16px;
      height: 16px;
      stroke: white;
      stroke-width: 2;
    }

    .chat-messages {
      /* Layout */
      flex: 1;
      padding: 16px;
      overflow-y: auto;

      /* Scrollbar styling */
      scrollbar-width: thin;
      scrollbar-color: #4a4a4a #2a2a2a;
    }

    .chat-messages::-webkit-scrollbar {
      width: 6px;
    }

    .chat-messages::-webkit-scrollbar-track {
      background: #2a2a2a;
    }

    .chat-messages::-webkit-scrollbar-thumb {
      background: #4a4a4a;
      border-radius: 3px;
    }

    .message {
      margin-bottom: 16px;
      animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .message-content {
      /* Layout */
      display: inline-block;
      padding: 12px 16px;
      max-width: 80%;

      /* Appearance */
      border-radius: 18px;

      /* Text */
      font-size: 14px;
      line-height: 1.4;
      word-wrap: break-word;
    }

    .message.user .message-content {
      background: var(--primary-color, #4F46E5);
      color: white;
      margin-left: auto;
      text-align: right;
    }

    .message.assistant .message-content {
      background: #3a3a3a;
      color: #e0e0e0;
    }

    .chat-input {
      /* Layout */
      display: flex;
      padding: 16px;
      gap: 8px;

      /* Appearance */
      background: #2a2a2a;
      border-top: 1px solid #3a3a3a;
    }

    .input-field {
      /* Reset */
      all: unset;

      /* Layout */
      flex: 1;
      padding: 12px 16px;

      /* Appearance */
      background: #1a1a1a;
      border: 1px solid #4a4a4a;
      border-radius: 20px;

      /* Text */
      color: white;
      font-size: 14px;

      /* Interaction */
      transition: border-color 0.2s ease;
    }

    .input-field:focus {
      outline: none;
      border-color: var(--primary-color, #4F46E5);
    }

    .input-field::placeholder {
      color: #888;
    }

    .send-button {
      /* Reset */
      all: unset;

      /* Layout */
      display: flex;
      align-items: center;
      justify-content: center;

      /* Size */
      width: 40px;
      height: 40px;

      /* Appearance */
      background: var(--primary-color, #4F46E5);
      border-radius: 50%;

      /* Interaction */
      cursor: pointer;

      /* Animation */
      transition: all 0.2s ease;
    }

    .send-button:hover {
      transform: scale(1.05);
    }

    .send-button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    .send-icon {
      width: 16px;
      height: 16px;
      fill: white;
    }

    /* Position variants */
    :host(.bottom-right) {
      bottom: 20px;
      right: 20px;
    }

    :host(.bottom-left) {
      bottom: 20px;
      left: 20px;
    }

    /* Responsive design */
    @media (max-width: 480px) {
      .chat-container {
        width: calc(100vw - 40px);
        height: calc(100vh - 40px);
        max-height: calc(100vh - 40px);
      }
    }
  `;

  // SVG Icons
  const ICONS = {
    chat: `<svg viewBox="0 0 24 24" class="bubble-icon">
      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
    </svg>`,

    close: `<svg viewBox="0 0 24 24" class="close-icon" fill="none">
      <path d="M18 6L6 18M6 6l12 12"/>
    </svg>`,

    send: `<svg viewBox="0 0 24 24" class="send-icon">
      <path d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z"/>
    </svg>`
  };

  // Main Widget Class
  class BublWidget extends HTMLElement {
    private shadow: ShadowRoot;
    private config: WidgetConfig;
    private isOpen: boolean = false;
    private messages: Array<{role: 'user' | 'assistant', content: string}> = [];

    constructor() {
      super();

      // Create shadow DOM for complete isolation
      this.shadow = this.attachShadow({ mode: 'closed' });

      // Get configuration
      this.config = this.getConfiguration();

      // Initialize widget
      this.initialize();
    }

    private getConfiguration(): WidgetConfig {
      const globalConfig = (window as any).Bubl?.config;

      if (!globalConfig?.websiteId) {
        throw new Error('Bubl Widget: websiteId is required');
      }

      return {
        websiteId: globalConfig.websiteId,
        primaryColor: globalConfig.primaryColor || '#4F46E5',
        secondaryColor: globalConfig.secondaryColor || '#FFFFFF',
        position: globalConfig.position || 'bottom-right',
        welcomeMessage: globalConfig.welcomeMessage || 'Hi there! How can I help you today?',
        headerText: globalConfig.headerText || 'Chat Assistant',
        initiallyOpen: globalConfig.initiallyOpen || false,
        apiBaseUrl: globalConfig.apiBaseUrl || window.location.origin
      };
    }

    private initialize(): void {
      // Set position class
      this.className = this.config.position || 'bottom-right';

      // Create styles
      const style = document.createElement('style');
      style.textContent = WIDGET_STYLES.replace(/var\(--primary-color[^)]*\)/g, this.config.primaryColor!);

      // Create widget HTML
      const container = document.createElement('div');
      container.className = 'widget-container';
      container.innerHTML = this.getWidgetHTML();

      // Append to shadow DOM
      this.shadow.appendChild(style);
      this.shadow.appendChild(container);

      // Bind events
      this.bindEvents();

      // Add welcome message
      if (this.config.welcomeMessage) {
        this.messages.push({
          role: 'assistant',
          content: this.config.welcomeMessage
        });
      }

      // Open initially if configured
      if (this.config.initiallyOpen) {
        this.openChat();
      }

      // Call ready callback
      this.callReadyCallback();
    }

    private getWidgetHTML(): string {
      return `
        <button class="bubble-button" id="bubble-btn">
          ${ICONS.chat}
        </button>

        <div class="chat-container" id="chat-container">
          <div class="chat-header">
            <span>${this.config.headerText}</span>
            <button class="close-button" id="close-btn">
              ${ICONS.close}
            </button>
          </div>

          <div class="chat-messages" id="messages"></div>

          <div class="chat-input">
            <input
              type="text"
              class="input-field"
              id="message-input"
              placeholder="Type your message..."
              autocomplete="off"
            />
            <button class="send-button" id="send-btn">
              ${ICONS.send}
            </button>
          </div>
        </div>
      `;
    }

    private bindEvents(): void {
      const bubbleBtn = this.shadow.getElementById('bubble-btn');
      const closeBtn = this.shadow.getElementById('close-btn');
      const sendBtn = this.shadow.getElementById('send-btn');
      const messageInput = this.shadow.getElementById('message-input') as HTMLInputElement;

      // Bubble button click
      bubbleBtn?.addEventListener('click', () => this.openChat());

      // Close button click
      closeBtn?.addEventListener('click', () => this.closeChat());

      // Send button click
      sendBtn?.addEventListener('click', () => this.sendMessage());

      // Enter key in input
      messageInput?.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          this.sendMessage();
        }
      });

      // Input validation
      messageInput?.addEventListener('input', () => {
        const sendButton = this.shadow.getElementById('send-btn') as HTMLButtonElement;
        if (sendButton) {
          sendButton.disabled = !messageInput.value.trim();
        }
      });
    }

    private openChat(): void {
      this.isOpen = true;

      const bubbleBtn = this.shadow.getElementById('bubble-btn');
      const chatContainer = this.shadow.getElementById('chat-container');

      if (bubbleBtn && chatContainer) {
        bubbleBtn.style.display = 'none';
        chatContainer.classList.add('open');

        // Render messages
        this.renderMessages();

        // Focus input
        const messageInput = this.shadow.getElementById('message-input') as HTMLInputElement;
        setTimeout(() => messageInput?.focus(), 100);

        // Track event
        this.trackEvent('chat_opened');
      }
    }

    private closeChat(): void {
      this.isOpen = false;

      const bubbleBtn = this.shadow.getElementById('bubble-btn');
      const chatContainer = this.shadow.getElementById('chat-container');

      if (bubbleBtn && chatContainer) {
        bubbleBtn.style.display = 'flex';
        chatContainer.classList.remove('open');

        // Track event
        this.trackEvent('chat_closed');
      }
    }

    private async sendMessage(): Promise<void> {
      const messageInput = this.shadow.getElementById('message-input') as HTMLInputElement;
      const message = messageInput?.value.trim();

      if (!message) return;

      // Clear input
      messageInput.value = '';

      // Add user message
      this.messages.push({ role: 'user', content: message });
      this.renderMessages();

      // Track event
      this.trackEvent('message_sent', { content: message.substring(0, 100) });

      try {
        // Send to API
        const response = await this.callChatAPI(message);

        // Add assistant response
        this.messages.push({ role: 'assistant', content: response });
        this.renderMessages();

        // Track event
        this.trackEvent('message_received', { contentLength: response.length });

      } catch (error) {
        console.error('Chat API error:', error);

        // Add error message
        this.messages.push({
          role: 'assistant',
          content: 'Sorry, I encountered an error. Please try again.'
        });
        this.renderMessages();

        // Track error
        this.trackEvent('error', { message: error.message });
      }
    }

    private renderMessages(): void {
      const messagesContainer = this.shadow.getElementById('messages');
      if (!messagesContainer) return;

      messagesContainer.innerHTML = this.messages.map(message => `
        <div class="message ${message.role}">
          <div class="message-content">${this.escapeHtml(message.content)}</div>
        </div>
      `).join('');

      // Scroll to bottom
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    private async callChatAPI(message: string): Promise<string> {
      const response = await fetch(`${this.config.apiBaseUrl}/api/chat/${this.config.websiteId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [{ role: 'user', content: message }],
          websiteId: this.config.websiteId,
          visitorId: this.getVisitorId(),
          conversationId: this.getConversationId()
        })
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();
      return data.content || data.message || 'No response received';
    }

    private getVisitorId(): string {
      const key = `bubl_visitor_${this.config.websiteId}`;
      let visitorId = localStorage.getItem(key);

      if (!visitorId) {
        visitorId = `visitor-${Math.random().toString(36).substring(2, 15)}`;
        localStorage.setItem(key, visitorId);
      }

      return visitorId;
    }

    private getConversationId(): string {
      // Generate new conversation ID for each session
      return `conv-${Math.random().toString(36).substring(2, 15)}`;
    }

    private trackEvent(eventType: string, metadata: any = {}): void {
      // Non-blocking analytics
      setTimeout(() => {
        fetch(`${this.config.apiBaseUrl}/api/analytics/track-event`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            websiteId: this.config.websiteId,
            visitorId: this.getVisitorId(),
            conversationId: this.getConversationId(),
            eventType,
            metadata: {
              ...metadata,
              isWidget: true,
              version: '2.0.0',
              source: 'embedded'
            }
          })
        }).catch(() => {}); // Silently fail analytics
      }, 0);
    }

    private escapeHtml(text: string): string {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }

    private callReadyCallback(): void {
      const callback = (window as any).Bubl?.onReady;
      if (typeof callback === 'function') {
        setTimeout(callback, 0);
      }
    }

    // Public API methods
    public open(): void {
      this.openChat();
    }

    public close(): void {
      this.closeChat();
    }

    public toggle(): void {
      if (this.isOpen) {
        this.closeChat();
      } else {
        this.openChat();
      }
    }

    public isWidgetOpen(): boolean {
      return this.isOpen;
    }
  }

  // Register custom element
  if (!customElements.get('bubl-widget-v2')) {
    customElements.define('bubl-widget-v2', BublWidget);
  }

  // Initialize widget
  function initializeWidget() {
    try {
      // Validate configuration
      const config = (window as any).Bubl?.config;
      if (!config?.websiteId) {
        console.error('Bubl Widget V2: websiteId is required in window.Bubl.config');
        return;
      }

      // Remove any existing widgets
      const existingWidget = document.querySelector('bubl-widget-v2');
      if (existingWidget) {
        existingWidget.remove();
      }

      // Create new widget
      const widget = document.createElement('bubl-widget-v2') as BublWidget;
      document.body.appendChild(widget);

      // Expose API
      window.BublWidgetV2.api = {
        open: () => widget.open(),
        close: () => widget.close(),
        toggle: () => widget.toggle(),
        isOpen: () => widget.isWidgetOpen()
      };

      console.log('Bubl Widget V2 initialized successfully');

    } catch (error) {
      console.error('Bubl Widget V2 initialization failed:', error);
    }
  }

  // Auto-initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeWidget);
  } else {
    initializeWidget();
  }

  // Export for global access
  window.BublWidgetV2 = {
    version: '2.0.0',
    initialize: initializeWidget,
    api: null // Will be set during initialization
  };

})();
