/**
 * Bubl Widget Loader
 * This script loads the Bubl widget on the customer's website.
 * It also tracks performance metrics to ensure optimal user experience.
 */
(function () {
	// Performance tracking - mark the start time
	const widgetLoadStart = performance.now();

	// Get the Bubl configuration from the global object
	const config = window.Bubl?.config;

	// Get the base URL from the script src
	// (No longer used for API requests, only for static assets)
	const scriptSrc = document.currentScript?.src || "";
	const baseUrl = scriptSrc.substring(
		0,
		scriptSrc.indexOf("/widget/v1/loader.js"),
	);

	// Determine the API base URL
	const apiBaseUrl = config?.apiBaseUrl ? config.apiBaseUrl : baseUrl;

	if (!config || !config.websiteId) {
		console.error("Bubl: Missing required configuration");
		return;
	}

	// Function to track performance metrics
	const trackPerformance = (
		metricName,
		value,
		websiteId,
		visitorId,
		conversationId,
	) => {
		try {
			fetch(`${apiBaseUrl}/api/analytics/track-event`, {
				method: "POST",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({
					websiteId,
					visitorId,
					conversationId,
					eventType: metricName,
					metadata: { value },
				}),
			}).catch((err) =>
				console.error("Failed to track performance metric:", err),
			);
		} catch (error) {
			console.error("Error tracking performance:", error);
		}
	};

	// Create the widget container
	const container = document.createElement("div");
	container.id = "bubl-widget-container";
	container.style.position = "fixed";
	container.style.zIndex = "99999999";
	container.style.bottom = "20px";
	container.style.display = "none"; // Hide initially until config is loaded
	container.style.transition = "all 0.3s ease"; // Add transition for smooth resizing
	container.style.pointerEvents = "none"; // Allow clicks to pass through to underlying elements
	container.style.width = "360px"; // Match iframe width
	container.style.height = "600px"; // Match iframe height
	container.style.maxHeight = "calc(100vh - 40px)";
	container.style.overflow = "hidden";
	// container.style.borderRadius = "10px";
	// container.style.boxShadow = "0 4px 24px rgba(0, 0, 0, 0.2)";

	// Default position to bottom-right
	container.style.right = "20px";

	// Add data attribute to track fullscreen state
	container.setAttribute("data-fullscreen", "false");

	// Create the bubble button
	const bubbleButton = document.createElement("button");
	bubbleButton.id = "bubl-bubble-button";
	bubbleButton.style.position = "fixed";
	bubbleButton.style.zIndex = "99999999";
	bubbleButton.style.bottom = "20px";
	bubbleButton.style.right = "20px"; // Default position to bottom-right
	bubbleButton.style.width = "60px";
	bubbleButton.style.height = "60px";
	bubbleButton.style.borderRadius = "50%";
	bubbleButton.style.backgroundColor = "#4F46E5"; // Default color, will be updated with config
	bubbleButton.style.border = "none";
	bubbleButton.style.boxShadow = "0 4px 8px rgba(0, 0, 0, 0.2)";
	bubbleButton.style.cursor = "pointer";
	bubbleButton.style.display = "none"; // Hide initially until config is loaded
	bubbleButton.style.transition = "all 0.3s ease";
	bubbleButton.setAttribute("aria-label", "Open chat");

	// Add chat icon to the bubble
	bubbleButton.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>`;

	// Append the container and bubble to the body
	document.body.appendChild(container);
	document.body.appendChild(bubbleButton);

	// Load the widget styles with preload for better performance
	const stylesPreload = document.createElement("link");
	stylesPreload.rel = "preload";
	stylesPreload.as = "style";
	stylesPreload.href = `${baseUrl}/widget/v1/styles.css`;
	document.head.appendChild(stylesPreload);

	// List of stylesheet URLs to load
	const stylesheetUrls = [
		`${baseUrl}/widget/v1/styles.css`,
		`${baseUrl}/widget/v1/bubble.css`,
		`${baseUrl}/widget/v1/fullscreen.css`,
	];

	let stylesLoaded = 0;
	const totalStyles = stylesheetUrls.length;
	let stylesAreReady = false;
	let stylesLoadTimeout;

	function onAllStylesLoaded() {
		stylesAreReady = true;
		console.log("Bubl: All styles loaded successfully");
		// Clear the timeout since styles loaded successfully
		if (stylesLoadTimeout) {
			clearTimeout(stylesLoadTimeout);
		}
	}

	function onStylesLoadTimeout() {
		console.warn("Bubl: Styles loading timeout, proceeding with fallback");
		stylesAreReady = true; // Allow widget to function even without all styles
	}

	// Set a timeout to ensure the widget works even if styles fail to load
	stylesLoadTimeout = setTimeout(onStylesLoadTimeout, 3000); // 3 second timeout

	// Show the bubble button immediately with inline styles as fallback
	bubbleButton.style.display = "flex";
	bubbleButton.style.alignItems = "center";
	bubbleButton.style.justifyContent = "center";

	// Add essential fallback styles to ensure the widget works without external CSS
	const fallbackStyles = document.createElement("style");
	fallbackStyles.textContent = `
		#bubl-bubble-button {
			display: flex !important;
			align-items: center !important;
			justify-content: center !important;
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
		}
		#bubl-widget-container {
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
			background: transparent !important;
		}
		#bubl-widget {
			border: none !important;
			background: transparent !important;
		}
	`;
	document.head.appendChild(fallbackStyles);

	// Dynamically load stylesheets and attach onload handlers
	for (const href of stylesheetUrls) {
		const link = document.createElement("link");
		link.rel = "stylesheet";
		link.href = href;
		link.onload = () => {
			stylesLoaded++;
			console.log(`Bubl: Loaded stylesheet ${stylesLoaded}/${totalStyles}: ${href}`);
			if (stylesLoaded === totalStyles) {
				onAllStylesLoaded();
			}
		};
		link.onerror = () => {
			console.warn(`Bubl: Failed to load stylesheet: ${href}`);
			stylesLoaded++; // Count failed loads to prevent hanging
			if (stylesLoaded === totalStyles) {
				onAllStylesLoaded();
			}
		};
		document.head.appendChild(link);
	}

	// Load web-vitals for performance monitoring with low priority
	const webVitalsScript = document.createElement("script");
	webVitalsScript.async = true;
	webVitalsScript.defer = true; // Defer loading to not block rendering
	webVitalsScript.src = `${baseUrl}/widget/v1/web-vitals.js`;

	// Set low priority for non-critical script
	if ("fetchPriority" in webVitalsScript) {
		webVitalsScript.fetchPriority = "low";
	}

	document.head.appendChild(webVitalsScript);

	// Create the widget iframe with optimized loading
	const iframe = document.createElement("iframe");
	iframe.id = "bubl-widget";
	iframe.title = "Bubl Chat Widget";
	iframe.style.border = "none";
	iframe.style.width = "360px";
	iframe.style.height = "600px";
	iframe.style.maxHeight = "calc(100vh - 40px)";
	iframe.style.transition = "all 0.3s ease";
	// iframe.style.borderRadius = "10px";
	iframe.style.backgroundColor = "transparent"; // Make iframe background transparent
	iframe.style.pointerEvents = "auto"; // Ensure iframe receives pointer events

	// Add data attribute to track fullscreen state
	iframe.setAttribute("data-fullscreen", "false");

	// Performance optimizations for iframe
	iframe.loading = "lazy"; // Lazy load the iframe
	iframe.setAttribute("importance", "low"); // Lower priority for non-visible iframe

	// Add window resize event listener to handle fullscreen mode
	window.addEventListener("resize", () => {
		// Check if the widget is in fullscreen mode
		if (iframe.getAttribute("data-fullscreen") === "true") {
			// Update iframe dimensions to match the viewport
			iframe.style.width = `${window.innerWidth}px`;
			iframe.style.height = `${window.innerHeight}px`;
		}
	});

	// Add sandbox attributes for security while allowing necessary functionality
	iframe.sandbox = "allow-scripts allow-same-origin allow-forms allow-popups";

	// Fetch the chat configuration from the public API
	fetch(`${apiBaseUrl}/api/widget/config/${config.websiteId}`)
		.then((response) => {
			if (!response.ok) {
				throw new Error(
					`Failed to fetch chat configuration: ${response.status}`,
				);
			}
			return response.json();
		})
		.then((serverConfig) => {
			// Merge server config with client config (client config takes precedence)
			const mergedConfig = {
				...serverConfig,
				...config,
				// Always use the websiteId from the client config
				websiteId: config.websiteId,
			};

			// Set position based on configuration
			if (mergedConfig.position === "bottom-left") {
				container.style.left = "20px";
				container.style.right = "auto";
			}

			// Set the iframe source with configuration parameters
			const params = new URLSearchParams({
				websiteId: mergedConfig.websiteId,
				primaryColor: mergedConfig.primaryColor || "#4F46E5",
				secondaryColor: mergedConfig.secondaryColor || "#FFFFFF",
				position: mergedConfig.position || "bottom-right",
				welcomeMessage:
					mergedConfig.welcomeMessage || "Hi there! How can I help you today?",
				headerText: mergedConfig.headerText || "Chat Assistant",
				initiallyOpen: mergedConfig.initiallyOpen ? "true" : "false",
			});

			// Generate or retrieve visitor ID from localStorage
			let visitorId;
			try {
				// Try to get the visitor ID from localStorage
				const storedVisitorId = localStorage.getItem(
					`bublai_visitor_${config.websiteId}`,
				);
				if (storedVisitorId) {
					visitorId = storedVisitorId;
					console.log("Bubl: Retrieved visitor ID from localStorage");
				} else {
					// Generate a new visitor ID if none exists
					visitorId = `visitor-${Math.random().toString(36).substring(2, 15)}`;
					// Store it in localStorage for future use
					localStorage.setItem(`bublai_visitor_${config.websiteId}`, visitorId);
					console.log("Bubl: Generated and stored new visitor ID");
				}
			} catch (e) {
				// Fallback if localStorage is not available
				visitorId = `visitor-${Math.random().toString(36).substring(2, 15)}`;
				console.log("Bubl: Generated visitor ID (localStorage not available)");
			}

			// Always generate a new conversation ID for each session (must be a valid UUID)
			let conversationId;

			// Use crypto.randomUUID if available (modern browsers)
			if (typeof crypto !== "undefined" && crypto.randomUUID) {
				conversationId = crypto.randomUUID();
			} else {
				// Fallback UUID v4 implementation for older browsers
				// This creates a valid UUID v4 format string
				conversationId = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
					/[xy]/g,
					(c) => {
						const r = (Math.random() * 16) | 0;
						const v = c === "x" ? r : (r & 0x3) | 0x8;
						return v.toString(16);
					},
				);
			}

			// Store these IDs for future use
			window.Bubl._internal = {
				visitorId,
				conversationId,
				loadStart: widgetLoadStart,
			};

			// Add event listener to track when iframe is fully loaded
			iframe.addEventListener("load", () => {
				const loadTime = performance.now() - widgetLoadStart;
				trackPerformance(
					"widget_loaded",
					loadTime,
					config.websiteId,
					visitorId,
					conversationId,
				);

				// Track web vitals if available
				if ("web-vitals" in window) {
					try {
						window["web-vitals"].getFID(({ value }) => {
							trackPerformance(
								"widget_fid",
								value,
								config.websiteId,
								visitorId,
								conversationId,
							);
						});

						window["web-vitals"].getLCP(({ value }) => {
							trackPerformance(
								"widget_lcp",
								value,
								config.websiteId,
								visitorId,
								conversationId,
							);
						});

						window["web-vitals"].getCLS(({ value }) => {
							trackPerformance(
								"widget_cls",
								value,
								config.websiteId,
								visitorId,
								conversationId,
							);
						});

						window["web-vitals"].getTTFB(({ value }) => {
							trackPerformance(
								"widget_ttfb",
								value,
								config.websiteId,
								visitorId,
								conversationId,
							);
						});

						// Approximate TTI (Time to Interactive)
						const tti = performance.now() - widgetLoadStart;
						trackPerformance(
							"widget_tti",
							tti,
							config.websiteId,
							visitorId,
							conversationId,
						);
					} catch (e) {
						console.error("Error tracking web vitals:", e);
					}
				}
			});

			// Add event listener for messages from the iframe content
			window.addEventListener("message", (event) => {
				// Check if the message is from our iframe
				if (event.source === iframe.contentWindow) {
					if (event?.data?.action) {
						// Handle fullscreen toggle from within the iframe
						if (event.data.action === "maximize") {
							window.Bubl.api.maximize();
						} else if (event.data.action === "restore") {
							window.Bubl.api.restore();
						} else if (event.data.action === "toggleMaximize") {
							window.Bubl.api.toggleMaximize();
						}
					}
				}
			});

			// Add visitor ID to the params
			params.append("visitorId", visitorId);

			// Set allowtransparency attribute to allow transparent background
			iframe.setAttribute("allowtransparency", "true");

			iframe.src = `${baseUrl}/widget/chat?${params.toString()}`;
			container.appendChild(iframe);

			// Update bubble position based on configuration
			if (mergedConfig.position === "bottom-left") {
				bubbleButton.style.left = "20px";
				bubbleButton.style.right = "auto";
			}

			// Add click event to the bubble button to toggle the chat widget
			bubbleButton.addEventListener("click", () => {
				console.log("Bubl: Bubble button clicked, stylesAreReady:", stylesAreReady);

				// Hide the bubble
				bubbleButton.style.display = "none";

				// Ensure iframe has proper dimensions
				iframe.style.width = "360px";
				iframe.style.height = "600px";
				iframe.style.maxHeight = "calc(100vh - 40px)";

				// Show the chat container
				container.style.display = "block";
				container.style.pointerEvents = "auto";
				container.style.width = "360px";
				container.style.height = "600px";
				container.style.maxHeight = "calc(100vh - 40px)";

				// Track bubble click event
				trackPerformance(
					"bubble_clicked",
					1,
					config.websiteId,
					visitorId,
					conversationId,
				);

				// Ensure the iframe is ready before sending the message
				// Add a small delay to ensure the iframe is ready to receive messages
				setTimeout(() => {
					// Send open message to iframe
					if (iframe.contentWindow) {
						console.log("Bubl: Sending open message to iframe");
						iframe.contentWindow.postMessage({ action: "open" }, "*");
					} else {
						console.error("Bubl: iframe.contentWindow not available");
					}
				}, 50); // 50ms delay should be enough without being noticeable
			});

			// Set up event handling to ensure links on the parent page work correctly
			// The container has pointer-events: none by default to let clicks pass through
			// The iframe has pointer-events: auto to capture its own events
			iframe.addEventListener("mouseenter", () => {
				// When mouse enters the iframe, ensure the container captures events
				container.style.pointerEvents = "auto";
			});

			iframe.addEventListener("mouseleave", () => {
				// When mouse leaves the iframe, let events pass through the container again
				container.style.pointerEvents = "none";
			});

			// Listen for close events from the iframe to show the bubble again
			window.addEventListener("message", (event) => {
				if (
					event.source === iframe.contentWindow &&
					event.data &&
					event.data.action === "close"
				) {
					// Hide the container
					container.style.display = "none";

					// Show the bubble again
					bubbleButton.style.display = "flex";
				}
			});

			// Call the onReady callback if provided
			if (typeof window.Bubl.onReady === "function") {
				window.Bubl.onReady();
			}
		})
		.catch((error) => {
			console.error("Bubl: Error loading configuration", error);
		});

	// Expose API methods
	window.Bubl.api = {
		open: () => {
			console.log("Bubl API: open method called, stylesAreReady:", stylesAreReady);

			// Hide the bubble
			bubbleButton.style.display = "none";

			// Ensure iframe has proper dimensions
			iframe.style.width = "360px";
			iframe.style.height = "600px";
			iframe.style.maxHeight = "calc(100vh - 40px)";

			// Show the container
			container.style.display = "block";
			container.style.pointerEvents = "auto";
			container.style.width = "360px";
			container.style.height = "600px";
			container.style.maxHeight = "calc(100vh - 40px)";

			// Ensure the iframe is ready before sending the message
			// Add a small delay to ensure the iframe is ready to receive messages
			setTimeout(() => {
				// Send message to iframe
				if (iframe.contentWindow) {
					console.log("Bubl API: Sending open message to iframe");
					iframe.contentWindow.postMessage({ action: "open" }, "*");
				} else {
					console.error("Bubl API: iframe.contentWindow not available");
				}
			}, 50); // 50ms delay should be enough without being noticeable
		},
		close: () => {
			console.log("Bubl API: close method called, stylesAreReady:", stylesAreReady);
			// Hide the container
			container.style.display = "none";

			// Show the bubble
			bubbleButton.style.display = "flex";

			// Send message to iframe
			if (iframe.contentWindow) {
				iframe.contentWindow.postMessage({ action: "close" }, "*");
			}
		},
		toggle: () => {
			console.log("Bubl API: toggle method called, stylesAreReady:", stylesAreReady);

			// Check if container is visible
			const isOpen = container.style.display === "block";
			console.log("Bubl API: Current state - isOpen:", isOpen);

			if (isOpen) {
				console.log("Bubl API: Closing widget");
				this.close();
			} else {
				console.log("Bubl API: Opening widget");
				this.open();
			}
		},
		maximize: () => {
			console.log("Bubl API: maximize method called, stylesAreReady:", stylesAreReady);
			if (iframe.contentWindow) {
				console.log("Bubl: Maximizing widget - BEFORE", {
					containerWidth: container.offsetWidth,
					containerHeight: container.offsetHeight,
					iframeWidth: iframe.offsetWidth,
					iframeHeight: iframe.offsetHeight,
					iframeStyle: iframe.style.cssText,
				});

				// Set container styles
				container.style.position = "fixed";
				container.style.top = "0";
				container.style.left = "0";
				container.style.right = "0";
				container.style.bottom = "0";
				container.style.margin = "0";
				container.style.padding = "0";
				container.style.zIndex = "2147483647";
				container.style.pointerEvents = "auto"; // Ensure container captures events in fullscreen mode

				// Set iframe styles directly
				iframe.style.position = "fixed";
				iframe.style.top = "0";
				iframe.style.left = "0";
				iframe.style.width = "100vw";
				iframe.style.height = "100vh";
				iframe.style.maxWidth = "100vw";
				iframe.style.maxHeight = "100vh";
				iframe.style.margin = "0";
				iframe.style.padding = "0";
				iframe.style.borderRadius = "0";
				iframe.style.zIndex = "2147483647";

				// Set data attribute for state tracking
				container.setAttribute("data-fullscreen", "true");
				iframe.setAttribute("data-fullscreen", "true");

				// Also send message to the iframe content
				iframe.contentWindow.postMessage({ action: "maximize" }, "*");

				// Log for debugging
				console.log("Bubl: Maximizing widget - AFTER", {
					containerWidth: container.offsetWidth,
					containerHeight: container.offsetHeight,
					iframeWidth: iframe.offsetWidth,
					iframeHeight: iframe.offsetHeight,
					iframeStyle: iframe.style.cssText,
				});
			}
		},
		restore: () => {
			console.log("Bubl API: restore method called, stylesAreReady:", stylesAreReady);
			if (iframe.contentWindow) {
				console.log("Bubl: Restoring widget - BEFORE", {
					containerWidth: container.offsetWidth,
					containerHeight: container.offsetHeight,
					iframeWidth: iframe.offsetWidth,
					iframeHeight: iframe.offsetHeight,
					iframeStyle: iframe.style.cssText,
				});

				// Reset container styles
				container.style.position = "fixed";
				container.style.top = "";
				container.style.left = "";
				container.style.right =
					mergedConfig?.position === "bottom-left" ? "" : "20px";
				container.style.left =
					mergedConfig?.position === "bottom-left" ? "20px" : "";
				container.style.bottom = "20px";
				container.style.margin = "";
				container.style.padding = "";
				container.style.zIndex = "99999999";
				container.style.width = "360px";
				container.style.height = "600px";
				container.style.maxHeight = "calc(100vh - 40px)";
				container.style.pointerEvents = "none"; // Reset to allow clicks to pass through

				// Reset iframe styles
				iframe.style.position = "";
				iframe.style.top = "";
				iframe.style.left = "";
				iframe.style.width = "360px";
				iframe.style.height = "600px";
				iframe.style.maxWidth = "";
				iframe.style.maxHeight = "calc(100vh - 40px)";
				iframe.style.margin = "";
				iframe.style.padding = "";
				// iframe.style.borderRadius = "10px";
				iframe.style.zIndex = "";

				// Set data attribute for state tracking
				container.setAttribute("data-fullscreen", "false");
				iframe.setAttribute("data-fullscreen", "false");

				// Also send message to the iframe content
				iframe.contentWindow.postMessage({ action: "restore" }, "*");

				// Log for debugging
				console.log("Bubl: Restoring widget - AFTER", {
					containerWidth: container.offsetWidth,
					containerHeight: container.offsetHeight,
					iframeWidth: iframe.offsetWidth,
					iframeHeight: iframe.offsetHeight,
					iframeStyle: iframe.style.cssText,
				});
			}
		},

		// Close the chat and show the bubble
		showBubble: () => {
			console.log("Bubl API: showBubble method called, stylesAreReady:", stylesAreReady);
			// Hide the container
			container.style.display = "none";

			// Show the bubble
			bubbleButton.style.display = "flex";
		},
		toggleMaximize: () => {
			console.log("Bubl API: toggleMaximize method called, stylesAreReady:", stylesAreReady);
			if (iframe.contentWindow) {
				const isFullscreen = iframe.getAttribute("data-fullscreen") === "true";

				// Make sure the bubble is hidden and container is visible
				bubbleButton.style.display = "none";
				container.style.display = "block";

				// Don't send the message first to avoid double-toggling
				if (isFullscreen) {
					// Call restore to handle both container and iframe
					this.restore();
				} else {
					// Call maximize to handle both container and iframe
					this.maximize();
				}
			}
		},
	};
})();
