<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Widget V1 vs V2 Comparison</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .widget-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .widget-section.v1 {
            border-left: 4px solid #f44336;
        }
        
        .widget-section.v2 {
            border-left: 4px solid #4CAF50;
        }
        
        .widget-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        
        .status-indicator.loading {
            background: #ff9800;
            animation: pulse 1s infinite;
        }
        
        .status-indicator.success {
            background: #4CAF50;
        }
        
        .status-indicator.error {
            background: #f44336;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }
        
        .btn.primary {
            background: #2196F3;
            color: white;
        }
        
        .btn.success {
            background: #4CAF50;
            color: white;
        }
        
        .btn.danger {
            background: #f44336;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .features-list {
            list-style: none;
            padding: 0;
        }
        
        .features-list li {
            padding: 5px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .features-list .icon {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .test-area {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .link-test {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        
        .link-test a {
            color: #2196F3;
            text-decoration: none;
            padding: 8px 12px;
            background: #f0f0f0;
            border-radius: 4px;
            transition: all 0.2s ease;
        }
        
        .link-test a:hover {
            background: #e0e0e0;
            transform: translateY(-1px);
        }
        
        .console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 11px;
            height: 150px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin: 15px 0;
        }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        
        .metric {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #2196F3;
        }
        
        .metric-label {
            font-size: 0.8em;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .comparison-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔄 Bubl Widget V1 vs V2 Comparison</h1>
        <p>Side-by-side comparison of the current implementation vs the complete rebuild</p>
    </div>

    <div class="comparison-container">
        <!-- V1 Widget Section -->
        <div class="widget-section v1">
            <div class="widget-title">
                <span class="status-indicator loading" id="v1-status"></span>
                Widget V1 (Current)
            </div>
            
            <ul class="features-list">
                <li><span class="icon">❌</span> Style leakage to host</li>
                <li><span class="icon">❌</span> Links become inactive</li>
                <li><span class="icon">❌</span> Complex iframe architecture</li>
                <li><span class="icon">❌</span> Global CSS injection</li>
                <li><span class="icon">❌</span> Performance issues</li>
                <li><span class="icon">❌</span> State management problems</li>
            </ul>
            
            <div class="controls">
                <button class="btn primary" onclick="testV1('open')">Open V1</button>
                <button class="btn" onclick="testV1('close')">Close V1</button>
                <button class="btn" onclick="testV1('toggle')">Toggle V1</button>
                <button class="btn danger" onclick="destroyV1()">Destroy V1</button>
            </div>
            
            <div class="metrics">
                <div class="metric">
                    <div class="metric-value" id="v1-files">3+</div>
                    <div class="metric-label">CSS Files</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="v1-elements">5+</div>
                    <div class="metric-label">DOM Elements</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="v1-isolation">❌</div>
                    <div class="metric-label">Isolation</div>
                </div>
            </div>
        </div>

        <!-- V2 Widget Section -->
        <div class="widget-section v2">
            <div class="widget-title">
                <span class="status-indicator loading" id="v2-status"></span>
                Widget V2 (Rebuild)
            </div>
            
            <ul class="features-list">
                <li><span class="icon">✅</span> Complete Shadow DOM isolation</li>
                <li><span class="icon">✅</span> Zero host interference</li>
                <li><span class="icon">✅</span> Single Web Component</li>
                <li><span class="icon">✅</span> CSS-in-JS scoped styles</li>
                <li><span class="icon">✅</span> Optimized performance</li>
                <li><span class="icon">✅</span> Robust state management</li>
            </ul>
            
            <div class="controls">
                <button class="btn success" onclick="testV2('open')">Open V2</button>
                <button class="btn" onclick="testV2('close')">Close V2</button>
                <button class="btn" onclick="testV2('toggle')">Toggle V2</button>
                <button class="btn danger" onclick="destroyV2()">Destroy V2</button>
            </div>
            
            <div class="metrics">
                <div class="metric">
                    <div class="metric-value" id="v2-files">0</div>
                    <div class="metric-label">CSS Files</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="v2-elements">1</div>
                    <div class="metric-label">DOM Elements</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="v2-isolation">✅</div>
                    <div class="metric-label">Isolation</div>
                </div>
            </div>
        </div>
    </div>

    <div class="test-area">
        <h3>🔗 Host Website Interaction Test</h3>
        <p>Test that links remain functional with both widget versions:</p>
        <div class="link-test">
            <a href="#test1" onclick="logLinkClick('Test Link 1')">Test Link 1</a>
            <a href="#test2" onclick="logLinkClick('Test Link 2')">Test Link 2</a>
            <a href="#test3" onclick="logLinkClick('Test Link 3')">Test Link 3</a>
            <a href="https://example.com" target="_blank" onclick="logLinkClick('External Link')">External Link</a>
        </div>
    </div>

    <div class="test-area">
        <h3>📊 Console Output</h3>
        <div id="console-output" class="console-output"></div>
    </div>

    <script>
        // Console capture
        const consoleOutput = document.getElementById('console-output');
        
        function logToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? 'ERROR' : type === 'warn' ? 'WARN' : 'LOG';
            consoleOutput.textContent += `[${timestamp}] ${prefix}: ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        // Override console methods
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logToConsole(args.join(' '), 'warn');
        };

        // Test functions
        function testV1(action) {
            if (window.Bubl?.api) {
                window.Bubl.api[action]();
                logToConsole(`V1 ${action} called`);
            } else {
                logToConsole('V1 API not available', 'error');
            }
        }

        function testV2(action) {
            if (window.BublWidgetV2?.api) {
                window.BublWidgetV2.api[action]();
                logToConsole(`V2 ${action} called`);
            } else {
                logToConsole('V2 API not available', 'error');
            }
        }

        function destroyV1() {
            const elements = document.querySelectorAll('#bubl-bubble-button, #bubl-widget-container');
            elements.forEach(el => el.remove());
            logToConsole('V1 elements destroyed');
        }

        function destroyV2() {
            const widget = document.querySelector('bubl-widget-v2');
            if (widget) {
                widget.remove();
                logToConsole('V2 widget destroyed');
            }
        }

        function logLinkClick(linkName) {
            logToConsole(`${linkName} clicked - links working correctly!`);
        }

        function updateStatus(version, status) {
            const indicator = document.getElementById(`${version}-status`);
            indicator.className = `status-indicator ${status}`;
        }

        // Initialize both widgets
        window.Bubl = {
            config: {
                websiteId: "9914d15e-edae-4db3-8eb1-c81f5e8c39d3",
                primaryColor: "#4F46E5",
                secondaryColor: "#FFFFFF",
                position: "bottom-right",
                welcomeMessage: "Hi! I'm the V1 widget (current implementation).",
                headerText: "V1 Assistant",
                initiallyOpen: false,
                apiBaseUrl: window.location.origin
            },
            onReady: function() {
                console.log('✅ V1 Widget loaded');
                updateStatus('v1', 'success');
            }
        };

        // Load V1 Widget
        const v1Script = document.createElement('script');
        v1Script.async = true;
        v1Script.src = '/widget/v1/loader.js';
        v1Script.onerror = () => {
            console.error('Failed to load V1 widget');
            updateStatus('v1', 'error');
        };
        document.head.appendChild(v1Script);

        // Load V2 Widget (with different config)
        setTimeout(() => {
            // Override config for V2
            window.Bubl.config.welcomeMessage = "Hi! I'm the V2 widget (complete rebuild with Shadow DOM).";
            window.Bubl.config.headerText = "V2 Assistant";
            window.Bubl.config.position = "bottom-left"; // Different position
            
            window.Bubl.onReady = function() {
                console.log('✅ V2 Widget loaded');
                updateStatus('v2', 'success');
                
                // Test Shadow DOM
                setTimeout(() => {
                    const widget = document.querySelector('bubl-widget-v2');
                    if (widget && widget.shadowRoot) {
                        console.log('✅ V2 Shadow DOM confirmed');
                    }
                }, 500);
            };

            const v2Script = document.createElement('script');
            v2Script.async = true;
            v2Script.src = '/widget/v2/widget.js';
            v2Script.onerror = () => {
                console.error('Failed to load V2 widget');
                updateStatus('v2', 'error');
            };
            document.head.appendChild(v2Script);
        }, 1000);

        console.log('🔄 Loading both V1 and V2 widgets for comparison...');
    </script>
</body>
</html>
