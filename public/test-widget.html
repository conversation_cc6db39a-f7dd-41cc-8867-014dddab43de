<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Widget - External Website Simulation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        p {
            line-height: 1.6;
            color: #666;
            margin-bottom: 15px;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .test-instructions {
            background: #e3f2fd;
            padding: 15px;
            border-left: 4px solid #2196f3;
            margin: 20px 0;
        }
        .link-test {
            margin: 20px 0;
        }
        .link-test a {
            color: #2196f3;
            text-decoration: none;
            padding: 10px 15px;
            background: #f0f0f0;
            border-radius: 4px;
            display: inline-block;
            margin: 5px;
        }
        .link-test a:hover {
            background: #e0e0e0;
        }
        .slow-loading-simulation {
            background: #fff3e0;
            padding: 15px;
            border-left: 4px solid #ff9800;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>External Website - Widget Test Page</h1>
        
        <div class="test-instructions">
            <h3>🧪 Widget First-Click Test Instructions</h3>
            <p><strong>Test the first-click bug fix:</strong></p>
            <ol>
                <li>Wait for the page to fully load</li>
                <li>Look for the Bubl chat bubble in the bottom-right corner</li>
                <li>Click the chat bubble <strong>once</strong></li>
                <li>The widget should open immediately on the first click</li>
                <li>If it doesn't open, try clicking again to see if it opens on the second click</li>
            </ol>
        </div>

        <p>This is a simulation of an external website where the Bubl chat widget is embedded. The widget should work correctly even when styles are loading or if there are network delays.</p>

        <div class="test-section">
            <h3>Website Content</h3>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
            
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        </div>

        <div class="link-test">
            <h3>Link Interaction Test</h3>
            <p>These links should remain clickable when the widget is embedded:</p>
            <a href="#section1">Link 1</a>
            <a href="#section2">Link 2</a>
            <a href="#section3">Link 3</a>
            <a href="https://example.com" target="_blank">External Link</a>
        </div>

        <div class="slow-loading-simulation">
            <h3>⚠️ Slow Loading Simulation</h3>
            <p>This page simulates a slower external website. The widget should still work on the first click even if styles are still loading.</p>
        </div>

        <div class="test-section">
            <h3>More Content</h3>
            <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>
            
            <p>Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.</p>
        </div>
    </div>

    <!-- Simulate slower loading by adding a delay before loading the widget -->
    <script>
        // Add some artificial delay to simulate slower external websites
        setTimeout(function() {
            // Set up the Bubl configuration
            window.Bubl = {
                config: {
                    websiteId: "9914d15e-edae-4db3-8eb1-c81f5e8c39d3", // Use a test website ID
                    primaryColor: "#4F46E5",
                    secondaryColor: "#FFFFFF",
                    position: "bottom-right",
                    welcomeMessage: "Hi! I'm here to help you test the widget.",
                    headerText: "Test Assistant",
                    initiallyOpen: false
                },
                onReady: function() {
                    console.log("Bubl widget is ready for testing!");
                }
            };

            // Load the widget script
            var script = document.createElement('script');
            script.async = true;
            script.src = 'http://localhost:3000/widget/v1/loader.js';
            var firstScript = document.getElementsByTagName('script')[0];
            firstScript.parentNode.insertBefore(script, firstScript);
        }, 1000); // 1 second delay to simulate slower loading
    </script>
</body>
</html>
