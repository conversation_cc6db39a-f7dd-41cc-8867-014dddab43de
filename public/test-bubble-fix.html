<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bubble Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }

        #console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>🔧 Bubble Hide Fix Test</h1>
        <p><strong>Testing:</strong> First-click bubble hiding and duplicate icon prevention</p>

        <div class="status info">
            <strong>Instructions:</strong>
            <ol>
                <li>Wait for the bubble to appear (should take ~1 second)</li>
                <li>Click the bubble ONCE</li>
                <li>Verify: Bubble disappears and chat opens</li>
                <li>Check: No duplicate icons should be visible</li>
                <li>Close the chat and repeat the test</li>
            </ol>
        </div>

        <div id="test-status" class="status info">
            Status: Waiting for widget to load...
        </div>

        <h3>Console Output:</h3>
        <div id="console-output"></div>
    </div>

    <script>
        // Capture console logs for debugging
        const consoleOutput = document.getElementById('console-output');
        const testStatus = document.getElementById('test-status');

        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            consoleOutput.textContent += args.join(' ') + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };

        const originalWarn = console.warn;
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            consoleOutput.textContent += 'WARN: ' + args.join(' ') + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };

        const originalError = console.error;
        console.error = function(...args) {
            originalError.apply(console, args);
            consoleOutput.textContent += 'ERROR: ' + args.join(' ') + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };

        // Track widget state
        let bubbleClicked = false;
        let widgetOpened = false;

        // Monitor for bubble clicks
        document.addEventListener('click', function(e) {
            if (e.target.id === 'bubl-bubble-button' || e.target.closest('#bubl-bubble-button')) {
                bubbleClicked = true;
                console.log('🔵 Bubble button clicked!');

                // Check if bubble is hidden and widget is shown after click
                setTimeout(() => {
                    const bubble = document.getElementById('bubl-bubble-button');
                    const container = document.getElementById('bubl-widget-container');
                    const iframe = document.getElementById('bubl-widget');

                    if (bubble && container) {
                        const bubbleHidden = bubble.style.display === 'none' ||
                                           window.getComputedStyle(bubble).display === 'none';
                        const containerVisible = container.style.display === 'block' ||
                                               window.getComputedStyle(container).display === 'block';

                        console.log('🔍 Widget state check:', {
                            bubbleHidden,
                            containerVisible,
                            bubbleDisplay: bubble.style.display,
                            containerDisplay: container.style.display,
                            iframeLoaded: iframe ? iframe.src : 'no iframe'
                        });

                        if (bubbleHidden && containerVisible) {
                            testStatus.className = 'status success';
                            testStatus.textContent = '✅ SUCCESS: Bubble hidden and widget visible!';
                            console.log('✅ Widget opened successfully');
                        } else if (bubbleHidden && !containerVisible) {
                            testStatus.className = 'status error';
                            testStatus.textContent = '❌ FAILED: Bubble hidden but widget not visible!';
                            console.log('❌ Widget container not visible');
                        } else if (!bubbleHidden) {
                            testStatus.className = 'status error';
                            testStatus.textContent = '❌ FAILED: Bubble still visible after click!';
                            console.log('❌ Bubble still visible:', bubble.style.display);
                        }
                    }
                }, 200); // Increased timeout to allow for iframe loading
            }
        });

        // Set up widget configuration
        setTimeout(function() {
            console.log('🚀 Loading Bubl widget...');

            window.Bubl = {
                config: {
                    websiteId: "9914d15e-edae-4db3-8eb1-c81f5e8c39d3",
                    primaryColor: "#4F46E5",
                    secondaryColor: "#FFFFFF",
                    position: "bottom-right",
                    welcomeMessage: "Testing bubble hide fix!",
                    headerText: "Test Assistant",
                    initiallyOpen: false
                },
                onReady: function() {
                    console.log('✅ Bubl widget is ready!');
                    testStatus.className = 'status info';
                    testStatus.textContent = '🟡 Widget loaded. Click the bubble to test!';

                    // Check for duplicate icons
                    setTimeout(() => {
                        const bubbles = document.querySelectorAll('[id*="bubble"], [class*="toggle"], [class*="chat-button"]');
                        console.log('🔍 Found potential bubble elements:', bubbles.length);

                        bubbles.forEach((el, i) => {
                            console.log(`Element ${i}:`, el.tagName, el.id, el.className);
                        });

                        if (bubbles.length > 1) {
                            console.warn('⚠️ Multiple bubble-like elements detected - possible duplicate!');
                        }
                    }, 500);
                }
            };

            // Load the widget script
            const script = document.createElement('script');
            script.async = true;
            script.src = 'http://localhost:3000/widget/v1/loader.js';
            script.onerror = function() {
                testStatus.className = 'status error';
                testStatus.textContent = '❌ Failed to load widget script';
                console.error('Failed to load widget script');
            };

            const firstScript = document.getElementsByTagName('script')[0];
            firstScript.parentNode.insertBefore(script, firstScript);

        }, 500); // Small delay to simulate real-world loading
    </script>
</body>
</html>
