<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bubl Widget V2 Test - Complete Rebuild</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .feature h3 {
            margin-top: 0;
            color: #ffd700;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #4CAF50;
        }
        
        .test-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .btn.primary {
            background: #4CAF50;
            border-color: #45a049;
        }
        
        .btn.secondary {
            background: #2196F3;
            border-color: #1976D2;
        }
        
        .btn.danger {
            background: #f44336;
            border-color: #d32f2f;
        }
        
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #f44336;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196F3;
            color: #2196F3;
        }
        
        .link-test {
            margin: 20px 0;
        }
        
        .link-test a {
            color: #ffd700;
            text-decoration: none;
            padding: 10px 15px;
            background: rgba(255, 215, 0, 0.1);
            border-radius: 5px;
            display: inline-block;
            margin: 5px;
            border: 1px solid rgba(255, 215, 0, 0.3);
            transition: all 0.3s ease;
        }
        
        .link-test a:hover {
            background: rgba(255, 215, 0, 0.2);
            transform: translateY(-2px);
        }
        
        .console-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin: 20px 0;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }
        
        .comparison-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
        }
        
        .comparison-item.old {
            border-left: 4px solid #f44336;
        }
        
        .comparison-item.new {
            border-left: 4px solid #4CAF50;
        }
        
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }
            
            .test-controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Bubl Widget V2 - Complete Rebuild</h1>
        
        <div class="features">
            <div class="feature">
                <h3>🛡️ Complete Isolation</h3>
                <p>Shadow DOM ensures zero style leakage to host websites. No more CSS conflicts or layout disruption.</p>
            </div>
            
            <div class="feature">
                <h3>⚡ Performance Optimized</h3>
                <p>Single bundle, CSS-in-JS, and optimized rendering for minimal impact on host websites.</p>
            </div>
            
            <div class="feature">
                <h3>🎯 Non-Intrusive</h3>
                <p>Links and interactions on host websites remain fully functional. No pointer-events interference.</p>
            </div>
            
            <div class="feature">
                <h3>🔧 Modern Architecture</h3>
                <p>Web Components with TypeScript, clean state management, and maintainable code structure.</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Widget V2 Testing</h3>
            <p><strong>Test the new implementation:</strong></p>
            
            <div class="test-controls">
                <button class="btn primary" onclick="testWidgetOpen()">Open Widget</button>
                <button class="btn secondary" onclick="testWidgetClose()">Close Widget</button>
                <button class="btn" onclick="testWidgetToggle()">Toggle Widget</button>
                <button class="btn danger" onclick="testWidgetDestroy()">Destroy Widget</button>
                <button class="btn" onclick="testWidgetRecreate()">Recreate Widget</button>
            </div>
            
            <div id="test-status" class="status info">
                Status: Widget V2 loading...
            </div>
        </div>

        <div class="link-test">
            <h3>🔗 Link Interaction Test</h3>
            <p>These links should remain fully functional with the new widget:</p>
            <a href="#test1" onclick="logLinkClick('Link 1')">Test Link 1</a>
            <a href="#test2" onclick="logLinkClick('Link 2')">Test Link 2</a>
            <a href="#test3" onclick="logLinkClick('Link 3')">Test Link 3</a>
            <a href="https://example.com" target="_blank" onclick="logLinkClick('External Link')">External Link</a>
        </div>

        <div class="comparison">
            <div class="comparison-item old">
                <h3>❌ V1 Issues (Fixed)</h3>
                <ul>
                    <li>Style leakage to host websites</li>
                    <li>Links becoming inactive</li>
                    <li>Complex iframe + DOM architecture</li>
                    <li>Global CSS injection</li>
                    <li>Pointer events interference</li>
                    <li>State management issues</li>
                </ul>
            </div>
            
            <div class="comparison-item new">
                <h3>✅ V2 Improvements</h3>
                <ul>
                    <li>Complete Shadow DOM isolation</li>
                    <li>Zero host website interference</li>
                    <li>Single Web Component architecture</li>
                    <li>CSS-in-JS with scoped styles</li>
                    <li>Clean event handling</li>
                    <li>Robust state management</li>
                </ul>
            </div>
        </div>

        <h3>📊 Console Output</h3>
        <div id="console-output" class="console-output"></div>
    </div>

    <script>
        // Console capture for debugging
        const consoleOutput = document.getElementById('console-output');
        const testStatus = document.getElementById('test-status');
        
        function logToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? 'ERROR' : type === 'warn' ? 'WARN' : 'LOG';
            consoleOutput.textContent += `[${timestamp}] ${prefix}: ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        // Override console methods
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logToConsole(args.join(' '), 'warn');
        };

        // Test functions
        function testWidgetOpen() {
            if (window.BublWidgetV2?.api) {
                window.BublWidgetV2.api.open();
                updateStatus('Widget opened via API', 'success');
            } else {
                updateStatus('Widget API not available', 'error');
            }
        }

        function testWidgetClose() {
            if (window.BublWidgetV2?.api) {
                window.BublWidgetV2.api.close();
                updateStatus('Widget closed via API', 'success');
            } else {
                updateStatus('Widget API not available', 'error');
            }
        }

        function testWidgetToggle() {
            if (window.BublWidgetV2?.api) {
                window.BublWidgetV2.api.toggle();
                const isOpen = window.BublWidgetV2.api.isOpen();
                updateStatus(`Widget toggled - now ${isOpen ? 'open' : 'closed'}`, 'success');
            } else {
                updateStatus('Widget API not available', 'error');
            }
        }

        function testWidgetDestroy() {
            const widget = document.querySelector('bubl-widget-v2');
            if (widget) {
                widget.remove();
                updateStatus('Widget destroyed', 'info');
            } else {
                updateStatus('No widget found to destroy', 'error');
            }
        }

        function testWidgetRecreate() {
            if (window.BublWidgetV2?.initialize) {
                window.BublWidgetV2.initialize();
                updateStatus('Widget recreated', 'success');
            } else {
                updateStatus('Widget initialization not available', 'error');
            }
        }

        function logLinkClick(linkName) {
            logToConsole(`${linkName} clicked successfully - no interference!`);
            updateStatus(`${linkName} clicked - links working correctly!`, 'success');
        }

        function updateStatus(message, type) {
            testStatus.textContent = message;
            testStatus.className = `status ${type}`;
        }

        // Initialize Widget V2
        window.Bubl = {
            config: {
                websiteId: "9914d15e-edae-4db3-8eb1-c81f5e8c39d3",
                primaryColor: "#4F46E5",
                secondaryColor: "#FFFFFF",
                position: "bottom-right",
                welcomeMessage: "Welcome to Bubl Widget V2! This is a complete rebuild with Shadow DOM isolation.",
                headerText: "Bubl V2 Assistant",
                initiallyOpen: false,
                apiBaseUrl: window.location.origin
            },
            onReady: function() {
                console.log('✅ Bubl Widget V2 is ready!');
                updateStatus('Widget V2 loaded successfully!', 'success');
                
                // Test Shadow DOM isolation
                setTimeout(() => {
                    const widget = document.querySelector('bubl-widget-v2');
                    if (widget && widget.shadowRoot) {
                        console.log('✅ Shadow DOM confirmed - complete isolation achieved');
                        logToConsole('Shadow DOM isolation confirmed');
                    } else {
                        console.log('⚠️ Shadow DOM not detected');
                        logToConsole('Shadow DOM not detected');
                    }
                }, 1000);
            }
        };

        // Load Widget V2
        const script = document.createElement('script');
        script.async = true;
        script.src = '/widget/v2/widget.js';
        script.onerror = function() {
            updateStatus('Failed to load Widget V2 script', 'error');
            console.error('Failed to load Widget V2 script');
        };
        
        document.head.appendChild(script);
        
        console.log('🚀 Loading Bubl Widget V2...');
    </script>
</body>
</html>
