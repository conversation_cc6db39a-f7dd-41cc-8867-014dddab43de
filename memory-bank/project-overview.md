# Bubl Project Overview

## Project Brief

**Bubl** (formerly Webchat AI) is a SaaS platform that enables businesses to add intelligent chat widgets to their websites. The platform crawls website content, creates vector embeddings for semantic search, and provides AI-powered customer support through embeddable chat widgets.

### Core Value Proposition
- **Easy Integration**: Simple embed code for any website
- **Intelligent Responses**: AI understands website content and provides relevant answers
- **Customizable**: Branded chat widgets with theme options
- **Analytics**: Detailed insights into customer interactions
- **Scalable**: Multiple pricing tiers for different business needs

## Current Status

### ✅ Completed Features
- **Authentication**: Clerk-based user management
- **Website Management**: Add, configure, and delete websites
- **Web Crawler**: Automated content extraction with Puppeteer/Browserless
- **Vector Search**: pgvector-based semantic search with Mistral embeddings
- **Chat Widget**: Embeddable React component with theme support
- **Analytics**: Conversation tracking and performance metrics
- **Plan Limits**: Usage-based restrictions per subscription tier
- **Security**: CSRF protection, input validation, security headers
- **Dashboard**: Website management and analytics interface

### 🔄 In Progress
- **Marketing Pages**: Public-facing website and pricing pages
- **Production Deployment**: Infrastructure setup and optimization
- **Performance Optimization**: RAG improvements and caching

### 📋 Planned Features
- **Advanced Analytics**: Core Web Vitals and detailed metrics
- **Multi-language Support**: Internationalization
- **Advanced Customization**: More widget styling options
- **API Access**: Public API for enterprise customers
- **Integrations**: Third-party platform connections

## Technical Stack

### Core Technologies
- **Framework**: Next.js 15 with TypeScript
- **Database**: PostgreSQL with Drizzle ORM and pgvector
- **Authentication**: Clerk
- **AI/ML**: MastrAI with Mistral models
- **Styling**: Tailwind CSS v4
- **UI Components**: Shadcn/ui with Sonner notifications

### Infrastructure
- **Deployment**: Self-hosted (preferred over Vercel)
- **Background Jobs**: Inngest
- **Web Crawling**: Puppeteer with Browserless
- **Environment**: Docker with docker-compose
- **Package Manager**: pnpm

## Business Model

### Subscription Tiers
1. **Free**: 1 website, 50 messages/day, basic analytics
2. **Pro**: 5 websites, 1000 messages/day, advanced analytics
3. **Business**: 20 websites, 5000 messages/day, priority support
4. **Enterprise**: Unlimited websites, custom limits, dedicated support

### Revenue Streams
- Monthly/annual subscriptions
- Usage-based overages
- Enterprise custom solutions
- Professional services (setup, customization)

## Key Metrics & Goals

### Current Metrics
- **Build Time**: ~6 seconds (excellent performance)
- **Database Tables**: 15+ with proper relationships
- **Test Coverage**: Basic e2e tests with Playwright
- **Security Score**: High (authentication, validation, headers)

### Target Goals
- **Response Time**: <200ms for vector search
- **Uptime**: 99.9% availability
- **Customer Satisfaction**: >90% positive feedback
- **Growth**: 100+ active websites by Q2 2024

## Competitive Advantages

1. **Easy Setup**: One-line embed code vs complex integrations
2. **Content Awareness**: Understands website context vs generic responses
3. **Customization**: Branded widgets vs white-label solutions
4. **Performance**: Fast vector search vs slow keyword matching
5. **Privacy**: Self-hosted option vs cloud-only solutions

## Recent Changes

### Rate Limiting Removal
- Simplified architecture by removing Upstash Redis dependency
- Reduced complexity and potential failure points
- Maintained security through plan limits and authentication
- Improved performance by eliminating Redis calls

### Analytics Enhancement
- Fixed database schema issues with chat_analytics_events table
- Implemented comprehensive event tracking
- Added performance metrics collection
- Created detailed analytics dashboard

### Security Improvements
- Implemented CSRF protection for state-changing operations
- Added comprehensive input validation with Zod
- Configured security headers middleware
- Removed rate limiting in favor of plan-based controls

## Domain & Branding

- **Primary Domain**: bublai.com
- **Brand Name**: Bubl (not Bubl.chat)
- **Target Market**: Small to medium businesses
- **Geographic Focus**: Initially English-speaking markets

## Next Priorities

1. **Marketing Launch**: Complete public website and pricing pages
2. **Production Deployment**: Set up production infrastructure
3. **Customer Acquisition**: Launch beta program with early adopters
4. **Feature Refinement**: Based on user feedback and analytics
5. **Scale Preparation**: Optimize for increased usage and traffic
