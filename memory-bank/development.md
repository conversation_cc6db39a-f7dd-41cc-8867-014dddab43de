# Development Guidelines

## Development Workflow

### Code Quality Standards
- **TypeScript**: Strict mode enabled, no `any` types allowed
- **Linting**: Biome for code formatting and linting
- **Build Validation**: Always run `pnpm build:check` before commits
- **Testing**: E2E tests with <PERSON><PERSON> for critical user flows
- **Documentation**: Update memory-bank when making significant changes

### Package Management
- **Package Manager**: pnpm for fast, efficient installs
- **Dependencies**: Use package managers instead of manual file editing
- **Version Control**: Exact version pinning for stability
- **Security**: Regular dependency updates and vulnerability scanning

### Database Management
- **ORM**: Drizzle ORM with schema-first approach
- **Migrations**: Use `pnpm db:generate` and `pnpm db:push`
- **Schema Changes**: Prefer Drizzle's approach over raw SQL
- **Relationships**: Use onDelete cascade constraints appropriately

### Environment Setup
```bash
# Development setup
pnpm install
pnpm db:push
pnpm dev

# Build validation
pnpm build:check

# Testing
pnpm test:e2e
```

## Development Patterns

### Component Architecture
- **UI Components**: Shadcn/ui with Radix primitives
- **State Management**: TanStack Query for server state
- **Form Handling**: React Hook Form with Zod validation
- **Error Boundaries**: Graceful error handling and user feedback

### API Development
- **Route Structure**: RESTful endpoints with consistent patterns
- **Validation**: Zod schemas for all inputs
- **Error Handling**: Standardized error responses
- **Authentication**: Clerk middleware for protected routes

### Background Jobs
- **Job Processing**: Inngest for reliable background processing
- **Error Handling**: Retry mechanisms and failure notifications
- **Monitoring**: Job status tracking and performance metrics

## Current Priorities

### 🔄 In Progress
1. **Marketing Pages**: Complete public website and pricing pages
2. **Production Deployment**: Set up production infrastructure
3. **Performance Optimization**: Improve RAG performance and caching

### 📋 Next Steps (Priority Order)
1. **Marketing Launch**: Finish public-facing website
2. **Beta Program**: Launch with early adopters
3. **Analytics Enhancement**: Advanced metrics and reporting
4. **Mobile Optimization**: Improve mobile experience
5. **API Documentation**: Public API for enterprise customers

## Technical Debt & Maintenance

### Recent Improvements
- ✅ **Rate Limiting Removal**: Simplified architecture, reduced complexity
- ✅ **Analytics Fix**: Resolved database schema issues
- ✅ **Security Enhancement**: CSRF protection and input validation
- ✅ **Build Optimization**: Fast builds with proper type checking

### Ongoing Maintenance
- **Dependency Updates**: Regular security and feature updates
- **Performance Monitoring**: Track build times and runtime performance
- **Code Cleanup**: Remove unused code and consolidate duplicates
- **Documentation**: Keep memory-bank updated with changes

## Testing Strategy

### Current Testing
- **Build Validation**: TypeScript compilation and linting
- **E2E Testing**: Playwright for critical user journeys
- **Manual Testing**: Feature validation and UI testing

### Planned Testing Improvements
- **Unit Tests**: Component and utility function testing
- **Integration Tests**: API endpoint validation
- **Performance Tests**: Load testing and optimization
- **Security Tests**: Vulnerability scanning and penetration testing

## Deployment Guidelines

### Development Environment
- **Local Setup**: Docker Compose with PostgreSQL and pgvector
- **Hot Reloading**: Next.js development server
- **Environment Variables**: Type-safe validation with t3oss/env

### Production Considerations
- **Self-hosted Deployment**: Preferred over Vercel for control
- **Database**: PostgreSQL with pgvector extension
- **Background Jobs**: Inngest for reliable processing
- **Monitoring**: Comprehensive logging and error tracking

## Code Review Guidelines

### Review Checklist
- [ ] TypeScript types are properly defined (no `any`)
- [ ] Input validation with Zod schemas
- [ ] Error handling and user feedback
- [ ] Security considerations (authentication, authorization)
- [ ] Performance implications
- [ ] Documentation updates if needed

### Best Practices
- **Small PRs**: Keep changes focused and reviewable
- **Clear Descriptions**: Explain what and why, not just how
- **Test Coverage**: Include tests for new functionality
- **Breaking Changes**: Document and communicate clearly

## Performance Guidelines

### Frontend Performance
- **Bundle Size**: Monitor and optimize JavaScript bundles
- **Image Optimization**: Use Next.js Image component
- **Lazy Loading**: Load components and routes on demand
- **Caching**: Leverage Next.js caching strategies

### Backend Performance
- **Database Queries**: Optimize with proper indexes
- **Vector Search**: Efficient similarity search with pgvector
- **Background Jobs**: Async processing for heavy operations
- **Response Times**: Target <200ms for API endpoints

## Security Guidelines

### Development Security
- **Environment Variables**: Never commit secrets to version control
- **Input Validation**: Validate all inputs at API boundaries
- **Authentication**: Use Clerk middleware for protected routes
- **HTTPS**: Always use HTTPS in production

### Code Security
- **SQL Injection**: Use ORM parameterized queries
- **XSS Prevention**: React's built-in protection + CSP headers
- **CSRF Protection**: Token-based protection for state changes
- **Dependency Security**: Regular vulnerability scanning

## Troubleshooting Common Issues

### Build Issues
- **TypeScript Errors**: Check for missing types or imports
- **Dependency Conflicts**: Clear node_modules and reinstall
- **Environment Variables**: Verify all required variables are set

### Database Issues
- **Schema Sync**: Run `pnpm db:push` to sync schema changes
- **Connection Issues**: Check database credentials and connectivity
- **Migration Errors**: Review schema changes and constraints

### Development Server Issues
- **Port Conflicts**: Check if port 3000 is available
- **Hot Reload**: Restart dev server if changes aren't reflected
- **Memory Issues**: Increase Node.js memory limit if needed

## Documentation Standards

### Code Documentation
- **JSDoc Comments**: For complex functions and APIs
- **README Files**: For packages and major features
- **Type Definitions**: Clear TypeScript interfaces and types

### Memory Bank Updates
- **Feature Changes**: Update features.md for new implementations
- **Architecture Changes**: Update architecture.md for structural changes
- **Security Changes**: Update security.md for security implementations
- **Progress Tracking**: Update development.md for completed tasks
