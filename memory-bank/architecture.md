# System Architecture

## Overview

Bubl is built as a modern SaaS application using Next.js 15 with a focus on performance, security, and scalability. The architecture follows a monolithic approach with clear separation of concerns and modular components.

## Tech Stack

### Frontend
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript (strict mode)
- **Styling**: Tailwind CSS v4
- **UI Components**: Shadcn/ui with Radix primitives
- **State Management**: TanStack Query for server state
- **Notifications**: Sonner for toast messages

### Backend
- **Runtime**: Node.js with Next.js API routes
- **Database**: PostgreSQL with pgvector extension
- **ORM**: Drizzle ORM with Dr<PERSON><PERSON><PERSON>od validation
- **Authentication**: Clerk with middleware integration
- **Background Jobs**: Inngest for async processing
- **Environment**: t3oss/env for type-safe environment variables

### AI & ML
- **Platform**: MastrAI with Vercel AI SDK
- **Models**: Mistral for chat and embeddings
- **Vector Search**: pgvector with cosine similarity
- **RAG**: Custom implementation with chunking and retrieval

### Infrastructure
- **Deployment**: Self-hosted (Docker preferred)
- **Database**: PostgreSQL with pgvector
- **Web Crawling**: Puppeteer with Browserless
- **Package Manager**: pnpm
- **Build Tool**: Next.js with TypeScript

## System Architecture Patterns

### Authentication & Authorization
- Clerk middleware with route-based protection
- Public routes: marketing, widget, chat endpoints
- Protected routes: dashboard and admin functionality
- User-website ownership validation

### Database Design
- Drizzle ORM with schema-first approach
- Foreign key constraints with cascade deletes
- UUID primary keys for security
- Timestamps for audit trails
- JSON fields for flexible metadata

### API Design
- RESTful endpoints with consistent patterns
- Zod validation for all inputs
- Standardized error responses
- CSRF protection for state changes
- Security headers on all responses

### Background Processing
- Inngest for reliable job processing
- Web crawling as background jobs
- Embedding generation in batches
- Retry mechanisms for failed operations

## Directory Structure

```
src/
├── app/                    # Next.js App Router
│   ├── (main)/            # Dashboard pages
│   ├── api/               # API routes
│   └── widget/            # Widget pages
├── components/            # React components
│   ├── ui/               # Shadcn/ui components
│   └── dashboard/        # Dashboard-specific components
├── lib/                  # Utility libraries
│   ├── db/              # Database configuration
│   ├── services/        # Business logic services
│   ├── validations/     # Zod schemas
│   └── utils/           # Helper functions
└── middleware.ts         # Next.js middleware
```

## Data Flow

### Website Crawling Flow
1. User adds website URL
2. Background job triggered via Inngest
3. Puppeteer crawls website content
4. Content processed and chunked
5. Embeddings generated via MastrAI
6. Data stored in PostgreSQL with pgvector

### Chat Interaction Flow
1. User sends message via widget
2. Message validated and stored
3. Vector search finds relevant content
4. Context passed to MastrAI
5. AI generates response
6. Response sent back to widget
7. Analytics event recorded

### Authentication Flow
1. User signs in via Clerk
2. Session established with JWT
3. Middleware validates on each request
4. User context available in components
5. Protected routes enforce authentication

## Security Architecture

### Authentication & Authorization
- Clerk handles user management and sessions
- Middleware enforces route protection
- User-website ownership validation
- Role-based access control ready

### Data Protection
- Input validation with Zod schemas
- SQL injection prevention via ORM
- XSS protection with React and CSP
- CSRF protection for state changes

### API Security
- Security headers on all responses
- CORS configuration for widget embedding
- Plan-based usage limits
- Request logging for monitoring

## Performance Optimizations

### Database
- pgvector indexes for fast similarity search
- Foreign key indexes for joins
- Connection pooling
- Query optimization with Drizzle

### Frontend
- Static generation where possible
- Component lazy loading
- Image optimization
- Bundle size monitoring

### Caching Strategy
- Next.js built-in caching
- Database query caching
- Static asset caching
- CDN for widget distribution

## Deployment Architecture

### Development
- Local PostgreSQL with Docker
- Hot reloading with Next.js
- Environment variable validation
- Automated testing with Playwright

### Production
- Self-hosted deployment preferred
- Docker containerization
- PostgreSQL with pgvector
- Reverse proxy (nginx)
- SSL/TLS termination

## Monitoring & Observability

### Logging
- Structured logging with timestamps
- Error tracking and alerting
- Performance metrics collection
- User interaction analytics

### Health Checks
- Database connectivity
- External service availability
- Background job status
- System resource monitoring

## Scalability Considerations

### Horizontal Scaling
- Stateless application design
- Database connection pooling
- Background job distribution
- CDN for static assets

### Vertical Scaling
- Efficient database queries
- Memory usage optimization
- CPU-intensive task offloading
- Resource monitoring and alerting

## Development Patterns

### Code Organization
- Feature-based directory structure
- Shared components and utilities
- Type-safe environment variables
- Consistent naming conventions

### Error Handling
- Centralized error boundaries
- Graceful degradation
- User-friendly error messages
- Comprehensive logging

### Testing Strategy
- Unit tests for utilities
- Integration tests for API routes
- E2E tests with Playwright
- Type checking with TypeScript

### Package Management
- pnpm for fast, efficient installs
- Exact version pinning
- Regular dependency updates
- Security vulnerability scanning
